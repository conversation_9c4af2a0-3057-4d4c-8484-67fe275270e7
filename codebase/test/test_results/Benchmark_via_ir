Compiling 44 files with 0.8.23
Solc 0.8.23 finished in 87.76s
Compiler run [33msuccessful with warnings:[0m
[1;33mWarning (2018)[0m[1;37m: Function state mutability can be restricted to view[0m
 [34m-->[0m test/TestEncoding.t.sol:8:5:
[34m  |[0m
[34m8 |[0m     [33mfunction testEncodeDecode() public {[0m
[34m  |[0m     [1;33m^ (Relevant source part starts here and spans across multiple lines).[0m


Ran 14 tests for test/test_benchmarks/BenchMarkingStorage.sol:BenchMarkTest
[32m[PASS][0m testBenchmarkBuySingleMultiplePriceDifferentUser() (gas: 3342504)
Logs:
  LIMIT BUY ORDER DIFFERENT PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  255589
  ITERATION  2  GAS USED:  172757
  ITERATION  3  GAS USED:  172757
  ITERATION  4  GAS USED:  172757
  ITERATION  5  GAS USED:  194889
  ITERATION  6  GAS USED:  172757
  ITERATION  7  GAS USED:  172757
  ITERATION  8  GAS USED:  172757
  ITERATION  9  GAS USED:  172757
  ITERATION  10  GAS USED:  172757

[32m[PASS][0m testBenchmarkBuySingleSamePriceDifferentUser() (gas: 3070130)
Logs:
  LIMIT BUY ORDERS SAME PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  255508
  ITERATION  2  GAS USED:  145326
  ITERATION  3  GAS USED:  145326
  ITERATION  4  GAS USED:  145326
  ITERATION  5  GAS USED:  145326
  ITERATION  6  GAS USED:  145326
  ITERATION  7  GAS USED:  145326
  ITERATION  8  GAS USED:  145326
  ITERATION  9  GAS USED:  145326
  ITERATION  10  GAS USED:  145326

[32m[PASS][0m testBenchmarkCancelBuyOrders() (gas: 2304055)
Logs:
  CANCEL BUY ORDERS 1LO : 
  ITERATION  1  GAS USED:  72806
  ITERATION  2  GAS USED:  50806
  ITERATION  3  GAS USED:  50806
  ITERATION  4  GAS USED:  50806
  ITERATION  5  GAS USED:  50806
  ITERATION  6  GAS USED:  50806
  ITERATION  7  GAS USED:  50806
  ITERATION  8  GAS USED:  50807
  ITERATION  9  GAS USED:  50807
  ITERATION  10  GAS USED:  56891

[32m[PASS][0m testBenchmarkCancelSellOrders() (gas: 2298072)
Logs:
  CANCEL SELL ORDERS 1LO :
  ITERATION  1  GAS USED:  72626
  ITERATION  2  GAS USED:  50626
  ITERATION  3  GAS USED:  50625
  ITERATION  4  GAS USED:  50626
  ITERATION  5  GAS USED:  50626
  ITERATION  6  GAS USED:  50626
  ITERATION  7  GAS USED:  50626
  ITERATION  8  GAS USED:  50627
  ITERATION  9  GAS USED:  50627
  ITERATION  10  GAS USED:  56747

[32m[PASS][0m testBenchmarkMarketBuyDifferentPriceIterative() (gas: 11985043)
Logs:
  MARKET BUY 3PP 1LO:
  ITERATION  1  GAS USED:  242175
  ITERATION  2  GAS USED:  220373
  ITERATION  3  GAS USED:  220665
  ITERATION  4  GAS USED:  220652
  ITERATION  5  GAS USED:  218086
  ITERATION  6  GAS USED:  217866
  ITERATION  7  GAS USED:  220564
  ITERATION  8  GAS USED:  220413
  ITERATION  9  GAS USED:  220648
  ITERATION  10  GAS USED:  212533

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative1LO() (gas: 11100198)
Logs:
  MARKET BUY 1PP 1LO :
  ITERATION  1  GAS USED:  157687
  ITERATION  2  GAS USED:  135687
  ITERATION  3  GAS USED:  135687
  ITERATION  4  GAS USED:  135687
  ITERATION  5  GAS USED:  135688
  ITERATION  6  GAS USED:  135687
  ITERATION  7  GAS USED:  135688
  ITERATION  8  GAS USED:  135688
  ITERATION  9  GAS USED:  135688
  ITERATION  10  GAS USED:  135688

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative2LO() (gas: 11468526)
Logs:
  MARKET BUY 1PP 2LO :
  ITERATION  1  GAS USED:  194511
  ITERATION  2  GAS USED:  172511
  ITERATION  3  GAS USED:  172511
  ITERATION  4  GAS USED:  172511
  ITERATION  5  GAS USED:  172512
  ITERATION  6  GAS USED:  172511
  ITERATION  7  GAS USED:  172512
  ITERATION  8  GAS USED:  172512
  ITERATION  9  GAS USED:  172512
  ITERATION  10  GAS USED:  172512

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative3LO() (gas: 11794461)
Logs:
  MARKET BUY 1PP 3LO :
  ITERATION  1  GAS USED:  231611
  ITERATION  2  GAS USED:  209611
  ITERATION  3  GAS USED:  209611
  ITERATION  4  GAS USED:  209611
  ITERATION  5  GAS USED:  209612
  ITERATION  6  GAS USED:  209611
  ITERATION  7  GAS USED:  209612
  ITERATION  8  GAS USED:  209612
  ITERATION  9  GAS USED:  209612
  ITERATION  10  GAS USED:  193611

[32m[PASS][0m testBenchmarkMarketSellDifferentPriceIterative() (gas: 12029232)
Logs:
  MARKET BUY 3PP 1LO : 
  ITERATION  1  GAS USED :  240270
  ITERATION  2  GAS USED :  218274
  ITERATION  3  GAS USED :  218411
  ITERATION  4  GAS USED :  215818
  ITERATION  5  GAS USED :  215453
  ITERATION  6  GAS USED :  218326
  ITERATION  7  GAS USED :  218151
  ITERATION  8  GAS USED :  218197
  ITERATION  9  GAS USED :  218600
  ITERATION  10  GAS USED :  211197

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative1LO() (gas: 11141515)
Logs:
  MARKET SELL 1PP 1LO: 
  ITERATION  1  GAS USED:  157198
  ITERATION  2  GAS USED:  135199
  ITERATION  3  GAS USED:  135198
  ITERATION  4  GAS USED:  135198
  ITERATION  5  GAS USED:  135199
  ITERATION  6  GAS USED:  135199
  ITERATION  7  GAS USED:  135199
  ITERATION  8  GAS USED:  135200
  ITERATION  9  GAS USED:  135200
  ITERATION  10  GAS USED:  135199

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative2LO() (gas: 11509397)
Logs:
  MARKET SELL 1PP 2LO: 
  ITERATION  1  GAS USED:  193829
  ITERATION  2  GAS USED:  171830
  ITERATION  3  GAS USED:  171829
  ITERATION  4  GAS USED:  171829
  ITERATION  5  GAS USED:  171830
  ITERATION  6  GAS USED:  171830
  ITERATION  7  GAS USED:  171830
  ITERATION  8  GAS USED:  171831
  ITERATION  9  GAS USED:  171831
  ITERATION  10  GAS USED:  171830

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative3LO() (gas: 11833255)
Logs:
  MARKET SELL 1PP 3LO: 
  ITERATION  1  GAS USED:  230736
  ITERATION  2  GAS USED:  208737
  ITERATION  3  GAS USED:  208736
  ITERATION  4  GAS USED:  208736
  ITERATION  5  GAS USED:  208737
  ITERATION  6  GAS USED:  208737
  ITERATION  7  GAS USED:  208737
  ITERATION  8  GAS USED:  208738
  ITERATION  9  GAS USED:  208738
  ITERATION  10  GAS USED:  193043

[32m[PASS][0m testBenchmarkSellSingleDifferentPriceSameUser() (gas: 1928033)
Logs:
  LIMIT SELL ORDER DIFFERENT PP: 
  ITERATION  1 GAS USED:  259886
  ITERATION  2 GAS USED:  154819
  ITERATION  3 GAS USED:  177063
  ITERATION  4 GAS USED:  154819
  ITERATION  5 GAS USED:  154820
  ITERATION  6 GAS USED:  177063
  ITERATION  7 GAS USED:  154820
  ITERATION  8 GAS USED:  154820
  ITERATION  9 GAS USED:  177064
  ITERATION  10 GAS USED:  150021

[32m[PASS][0m testBenchmarkSellSingleSamePriceSameUser() (gas: 1812196)
Logs:
  LIMIT SELL ORDER 1PP: 
  ITERATION  1 GAS USED:  259732
  ITERATION  2 GAS USED:  149559
  ITERATION  3 GAS USED:  149559
  ITERATION  4 GAS USED:  149560
  ITERATION  5 GAS USED:  149560
  ITERATION  6 GAS USED:  149560
  ITERATION  7 GAS USED:  149560
  ITERATION  8 GAS USED:  149560
  ITERATION  9 GAS USED:  149561
  ITERATION  10 GAS USED:  144760

Suite result: [32mok[0m. [32m14[0m passed; [31m0[0m failed; [33m0[0m skipped; finished in 34.31ms (179.09ms CPU time)

Ran 1 test suite in 734.61ms (34.31ms CPU time): [32m14[0m tests passed, [31m0[0m failed, [33m0[0m skipped (14 total tests)
