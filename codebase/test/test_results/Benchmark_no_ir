Compiling 44 files with 0.8.23
Solc 0.8.23 finished in 7.55s
Compiler run [33msuccessful with warnings:[0m
[1;33mWarning (2018)[0m[1;37m: Function state mutability can be restricted to view[0m
 [34m-->[0m test/TestEncoding.t.sol:8:5:
[34m  |[0m
[34m8 |[0m     [33mfunction testEncodeDecode() public {[0m
[34m  |[0m     [1;33m^ (Relevant source part starts here and spans across multiple lines).[0m


Ran 14 tests for test/test_benchmarks/BenchMarkingStorage.sol:BenchMarkTest
[32m[PASS][0m testBenchmarkBuySingleMultiplePriceDifferentUser() (gas: 3373274)
Logs:
  LIMIT BUY ORDER DIFFERENT PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  252070
  ITERATION  2  GAS USED:  174146
  ITERATION  3  GAS USED:  174146
  ITERATION  4  GAS USED:  174146
  ITERATION  5  GAS USED:  196270
  ITERATION  6  GAS USED:  174146
  ITERATION  7  GAS USED:  174146
  ITERATION  8  GAS USED:  174146
  ITERATION  9  GAS USED:  174146
  ITERATION  10  GAS USED:  174146

[32m[PASS][0m testBenchmarkBuySingleSamePriceDifferentUser() (gas: 3101743)
Logs:
  LIMIT BUY ORDERS SAME PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  252070
  ITERATION  2  GAS USED:  146663
  ITERATION  3  GAS USED:  146663
  ITERATION  4  GAS USED:  146663
  ITERATION  5  GAS USED:  146663
  ITERATION  6  GAS USED:  146663
  ITERATION  7  GAS USED:  146663
  ITERATION  8  GAS USED:  146663
  ITERATION  9  GAS USED:  146663
  ITERATION  10  GAS USED:  146663

[32m[PASS][0m testBenchmarkCancelBuyOrders() (gas: 2323501)
Logs:
  CANCEL BUY ORDERS 1LO : 
  ITERATION  1  GAS USED:  68180
  ITERATION  2  GAS USED:  51080
  ITERATION  3  GAS USED:  51080
  ITERATION  4  GAS USED:  51081
  ITERATION  5  GAS USED:  51081
  ITERATION  6  GAS USED:  51081
  ITERATION  7  GAS USED:  51082
  ITERATION  8  GAS USED:  51081
  ITERATION  9  GAS USED:  51082
  ITERATION  10  GAS USED:  57196

[32m[PASS][0m testBenchmarkCancelSellOrders() (gas: 2317464)
Logs:
  CANCEL SELL ORDERS 1LO :
  ITERATION  1  GAS USED:  67964
  ITERATION  2  GAS USED:  50864
  ITERATION  3  GAS USED:  50864
  ITERATION  4  GAS USED:  50864
  ITERATION  5  GAS USED:  50864
  ITERATION  6  GAS USED:  50865
  ITERATION  7  GAS USED:  50865
  ITERATION  8  GAS USED:  50864
  ITERATION  9  GAS USED:  50865
  ITERATION  10  GAS USED:  57023

[32m[PASS][0m testBenchmarkMarketBuyDifferentPriceIterative() (gas: 12166701)
Logs:
  MARKET BUY 3PP 1LO:
  ITERATION  1  GAS USED:  246046
  ITERATION  2  GAS USED:  229024
  ITERATION  3  GAS USED:  229145
  ITERATION  4  GAS USED:  229137
  ITERATION  5  GAS USED:  226659
  ITERATION  6  GAS USED:  226341
  ITERATION  7  GAS USED:  229107
  ITERATION  8  GAS USED:  229035
  ITERATION  9  GAS USED:  229139
  ITERATION  10  GAS USED:  219427

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative1LO() (gas: 11239405)
Logs:
  MARKET BUY 1PP 1LO :
  ITERATION  1  GAS USED:  156985
  ITERATION  2  GAS USED:  139886
  ITERATION  3  GAS USED:  139885
  ITERATION  4  GAS USED:  139885
  ITERATION  5  GAS USED:  139885
  ITERATION  6  GAS USED:  139886
  ITERATION  7  GAS USED:  139886
  ITERATION  8  GAS USED:  139886
  ITERATION  9  GAS USED:  139886
  ITERATION  10  GAS USED:  139887

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative2LO() (gas: 11624692)
Logs:
  MARKET BUY 1PP 2LO :
  ITERATION  1  GAS USED:  195213
  ITERATION  2  GAS USED:  178114
  ITERATION  3  GAS USED:  178113
  ITERATION  4  GAS USED:  178113
  ITERATION  5  GAS USED:  178113
  ITERATION  6  GAS USED:  178114
  ITERATION  7  GAS USED:  178114
  ITERATION  8  GAS USED:  178114
  ITERATION  9  GAS USED:  178114
  ITERATION  10  GAS USED:  178115

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative3LO() (gas: 11965155)
Logs:
  MARKET BUY 1PP 3LO :
  ITERATION  1  GAS USED:  233725
  ITERATION  2  GAS USED:  216626
  ITERATION  3  GAS USED:  216625
  ITERATION  4  GAS USED:  216625
  ITERATION  5  GAS USED:  216625
  ITERATION  6  GAS USED:  216626
  ITERATION  7  GAS USED:  216626
  ITERATION  8  GAS USED:  216626
  ITERATION  9  GAS USED:  216626
  ITERATION  10  GAS USED:  200813

[32m[PASS][0m testBenchmarkMarketSellDifferentPriceIterative() (gas: 12207370)
Logs:
  MARKET BUY 3PP 1LO : 
  ITERATION  1  GAS USED :  243514
  ITERATION  2  GAS USED :  226415
  ITERATION  3  GAS USED :  226500
  ITERATION  4  GAS USED :  223983
  ITERATION  5  GAS USED :  223608
  ITERATION  6  GAS USED :  226450
  ITERATION  7  GAS USED :  226343
  ITERATION  8  GAS USED :  226373
  ITERATION  9  GAS USED :  226605
  ITERATION  10  GAS USED :  217602

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative1LO() (gas: 11277771)
Logs:
  MARKET SELL 1PP 1LO: 
  ITERATION  1  GAS USED:  156178
  ITERATION  2  GAS USED:  139077
  ITERATION  3  GAS USED:  139078
  ITERATION  4  GAS USED:  139078
  ITERATION  5  GAS USED:  139078
  ITERATION  6  GAS USED:  139078
  ITERATION  7  GAS USED:  139078
  ITERATION  8  GAS USED:  139078
  ITERATION  9  GAS USED:  139079
  ITERATION  10  GAS USED:  139078

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative2LO() (gas: 11658444)
Logs:
  MARKET SELL 1PP 2LO: 
  ITERATION  1  GAS USED:  194133
  ITERATION  2  GAS USED:  177032
  ITERATION  3  GAS USED:  177033
  ITERATION  4  GAS USED:  177033
  ITERATION  5  GAS USED:  177033
  ITERATION  6  GAS USED:  177033
  ITERATION  7  GAS USED:  177033
  ITERATION  8  GAS USED:  177033
  ITERATION  9  GAS USED:  177034
  ITERATION  10  GAS USED:  177033

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative3LO() (gas: 11995382)
Logs:
  MARKET SELL 1PP 3LO: 
  ITERATION  1  GAS USED:  232372
  ITERATION  2  GAS USED:  215271
  ITERATION  3  GAS USED:  215272
  ITERATION  4  GAS USED:  215272
  ITERATION  5  GAS USED:  215272
  ITERATION  6  GAS USED:  215272
  ITERATION  7  GAS USED:  215272
  ITERATION  8  GAS USED:  215272
  ITERATION  9  GAS USED:  215273
  ITERATION  10  GAS USED:  199680

[32m[PASS][0m testBenchmarkSellSingleDifferentPriceSameUser() (gas: 1945834)
Logs:
  LIMIT SELL ORDER DIFFERENT PP: 
  ITERATION  1 GAS USED:  256380
  ITERATION  2 GAS USED:  156209
  ITERATION  3 GAS USED:  178462
  ITERATION  4 GAS USED:  156209
  ITERATION  5 GAS USED:  156210
  ITERATION  6 GAS USED:  178463
  ITERATION  7 GAS USED:  156210
  ITERATION  8 GAS USED:  156214
  ITERATION  9 GAS USED:  178463
  ITERATION  10 GAS USED:  151410

[32m[PASS][0m testBenchmarkSellSingleSamePriceSameUser() (gas: 1829066)
Logs:
  LIMIT SELL ORDER 1PP: 
  ITERATION  1 GAS USED:  256282
  ITERATION  2 GAS USED:  150882
  ITERATION  3 GAS USED:  150882
  ITERATION  4 GAS USED:  150882
  ITERATION  5 GAS USED:  150881
  ITERATION  6 GAS USED:  150882
  ITERATION  7 GAS USED:  150882
  ITERATION  8 GAS USED:  150886
  ITERATION  9 GAS USED:  150882
  ITERATION  10 GAS USED:  146082

Suite result: [32mok[0m. [32m14[0m passed; [31m0[0m failed; [33m0[0m skipped; finished in 45.30ms (246.04ms CPU time)

Ran 1 test suite in 653.31ms (45.30ms CPU time): [32m14[0m tests passed, [31m0[0m failed, [33m0[0m skipped (14 total tests)
