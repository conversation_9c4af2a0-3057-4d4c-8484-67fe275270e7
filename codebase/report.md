# Aderyn Analysis Report

This report was generated by [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON>/aderyn), a static analysis tool built by [<PERSON><PERSON><PERSON>](https://cyfrin.io), a blockchain security company. This report is not a substitute for manual audit or security review. It should not be relied upon for any purpose other than to assist in the identification of potential security vulnerabilities.
# Table of Contents

- [Summary](#summary)
  - [Files Summary](#files-summary)
  - [Files Details](#files-details)
  - [Issue Summary](#issue-summary)
- [High Issues](#high-issues)
  - [H-1: `abi.encodePacked()` should not be used with dynamic types when passing the result to a hash function such as `keccak256()`](#h-1-abiencodepacked-should-not-be-used-with-dynamic-types-when-passing-the-result-to-a-hash-function-such-as-keccak256)
- [Low Issues](#low-issues)
  - [L-1: Centralization Risk for trusted owners](#l-1-centralization-risk-for-trusted-owners)
  - [L-2: Unsafe ERC20 Operations should not be used](#l-2-unsafe-erc20-operations-should-not-be-used)
  - [L-3: Solidity pragma should be specific, not wide](#l-3-solidity-pragma-should-be-specific-not-wide)
  - [L-4: Missing checks for `address(0)` when assigning values to address state variables](#l-4-missing-checks-for-address0-when-assigning-values-to-address-state-variables)
  - [L-5: `public` functions not used internally could be marked `external`](#l-5-public-functions-not-used-internally-could-be-marked-external)
  - [L-6: Define and use `constant` variables instead of using literals](#l-6-define-and-use-constant-variables-instead-of-using-literals)
  - [L-7: Event is missing `indexed` fields](#l-7-event-is-missing-indexed-fields)
  - [L-8: Empty `require()` / `revert()` statements](#l-8-empty-require--revert-statements)
  - [L-9: PUSH0 is not supported by all chains](#l-9-push0-is-not-supported-by-all-chains)
  - [L-10: Modifiers invoked only once can be shoe-horned into the function](#l-10-modifiers-invoked-only-once-can-be-shoe-horned-into-the-function)
  - [L-11: Empty Block](#l-11-empty-block)
  - [L-12: Large literal values multiples of 10000 can be replaced with scientific notation](#l-12-large-literal-values-multiples-of-10000-can-be-replaced-with-scientific-notation)
  - [L-13: Internal functions called only once can be inlined](#l-13-internal-functions-called-only-once-can-be-inlined)
  - [L-14: Contract still has TODOs](#l-14-contract-still-has-todos)
  - [L-15: Inconsistency in declaring uint256/uint (or) int256/int variables within a contract](#l-15-inconsistency-in-declaring-uint256uint-or-int256int-variables-within-a-contract)
  - [L-16: Loop contains `require`/`revert` statements](#l-16-loop-contains-requirerevert-statements)


# Summary

## Files Summary

| Key | Value |
| --- | --- |
| .sol Files | 12 |
| Total nSLOC | 1072 |


## Files Details

| Filepath | nSLOC |
| --- | --- |
| contracts/MarginAccount.sol | 96 |
| contracts/OrderBook.sol | 436 |
| contracts/Router.sol | 210 |
| contracts/interfaces/IMarginAccount.sol | 10 |
| contracts/interfaces/IOrderBook.sol | 40 |
| contracts/interfaces/IRouter.sol | 3 |
| contracts/libraries/BitMath.sol | 90 |
| contracts/libraries/IERC20.sol | 12 |
| contracts/libraries/MintableERC20.sol | 11 |
| contracts/libraries/OrderLinkedList.sol | 40 |
| contracts/libraries/TreeMath.sol | 124 |
| **Total** | **1072** |


## Issue Summary

| Category | No. of Issues |
| --- | --- |
| High | 1 |
| Low | 16 |


# High Issues

## H-1: `abi.encodePacked()` should not be used with dynamic types when passing the result to a hash function such as `keccak256()`

Use `abi.encode()` instead which will pad items to 32 bytes, which will [prevent hash collisions](https://docs.soliditylang.org/en/v0.8.13/abi-spec.html#non-standard-packed-mode) (e.g. `abi.encodePacked(0x123,0x456)` => `0x123456` => `abi.encodePacked(0x1,0x23456)`, but `abi.encode(0x123,0x456)` => `0x0...1230...456`). Unless there is a compelling reason, `abi.encode` should be preferred. If there is only one argument to `abi.encodePacked()` it can often be cast to `bytes()` or `bytes32()` [instead](https://ethereum.stackexchange.com/questions/30912/how-to-compare-strings-in-solidity#answer-82739).
If all arguments are strings and or bytes, `bytes.concat()` should be used instead.

