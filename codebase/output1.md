No files changed, compilation skipped

Ran 14 tests for test/test_benchmarks/BenchMarkingStorage.sol:BenchMarkTest
[32m[PASS][0m testBenchmarkBuySingleMultiplePriceDifferentUser() (gas: 3345156)
Logs:
  LIMIT BUY ORDER DIFFERENT PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  250567
  ITERATION  2  GAS USED:  172635
  ITERATION  3  GAS USED:  172635
  ITERATION  4  GAS USED:  172635
  ITERATION  5  GAS USED:  194767
  ITERATION  6  GAS USED:  172635
  ITERATION  7  GAS USED:  172635
  ITERATION  8  GAS USED:  172635
  ITERATION  9  GAS USED:  172635
  ITERATION  10  GAS USED:  172635

[32m[PASS][0m testBenchmarkBuySingleSamePriceDifferentUser() (gas: 3072722)
Logs:
  LIMIT BUY ORDERS SAME PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  250486
  ITERATION  2  GAS USED:  145204
  ITERATION  3  GAS USED:  145204
  ITERATION  4  GAS USED:  145204
  ITERATION  5  GAS USED:  145204
  ITERATION  6  GAS USED:  145204
  ITERATION  7  GAS USED:  145204
  ITERATION  8  GAS USED:  145204
  ITERATION  9  GAS USED:  145204
  ITERATION  10  GAS USED:  145204

[32m[PASS][0m testBenchmarkCancelBuyOrders() (gas: 2288645)
Logs:
  CANCEL BUY ORDERS 1LO : 
  ITERATION  1  GAS USED:  67884
  ITERATION  2  GAS USED:  50784
  ITERATION  3  GAS USED:  50784
  ITERATION  4  GAS USED:  50784
  ITERATION  5  GAS USED:  50785
  ITERATION  6  GAS USED:  50785
  ITERATION  7  GAS USED:  50785
  ITERATION  8  GAS USED:  50785
  ITERATION  9  GAS USED:  50785
  ITERATION  10  GAS USED:  56876

[32m[PASS][0m testBenchmarkCancelSellOrders() (gas: 2283063)
Logs:
  CANCEL SELL ORDERS 1LO :
  ITERATION  1  GAS USED:  67694
  ITERATION  2  GAS USED:  50594
  ITERATION  3  GAS USED:  50594
  ITERATION  4  GAS USED:  50594
  ITERATION  5  GAS USED:  50594
  ITERATION  6  GAS USED:  50594
  ITERATION  7  GAS USED:  50594
  ITERATION  8  GAS USED:  50594
  ITERATION  9  GAS USED:  50595
  ITERATION  10  GAS USED:  56724

[32m[PASS][0m testBenchmarkMarketBuyDifferentPriceIterative() (gas: 12077578)
Logs:
  MARKET BUY 3PP 1LO:
  ITERATION  1  GAS USED:  242144
  ITERATION  2  GAS USED:  225242
  ITERATION  3  GAS USED:  225533
  ITERATION  4  GAS USED:  225520
  ITERATION  5  GAS USED:  222954
  ITERATION  6  GAS USED:  222735
  ITERATION  7  GAS USED:  225432
  ITERATION  8  GAS USED:  225281
  ITERATION  9  GAS USED:  225516
  ITERATION  10  GAS USED:  216431

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative1LO() (gas: 10888409)
Logs:
  MARKET BUY 1PP 1LO :
  ITERATION  1  GAS USED:  137099
  ITERATION  2  GAS USED:  135650
  ITERATION  3  GAS USED:  135649
  ITERATION  4  GAS USED:  135649
  ITERATION  5  GAS USED:  135649
  ITERATION  6  GAS USED:  135650
  ITERATION  7  GAS USED:  135650
  ITERATION  8  GAS USED:  135650
  ITERATION  9  GAS USED:  135650
  ITERATION  10  GAS USED:  135650

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative2LO() (gas: 11248847)
Logs:
  MARKET BUY 1PP 2LO :
  ITERATION  1  GAS USED:  172804
  ITERATION  2  GAS USED:  172805
  ITERATION  3  GAS USED:  172804
  ITERATION  4  GAS USED:  172804
  ITERATION  5  GAS USED:  172804
  ITERATION  6  GAS USED:  172805
  ITERATION  7  GAS USED:  172805
  ITERATION  8  GAS USED:  172805
  ITERATION  9  GAS USED:  172805
  ITERATION  10  GAS USED:  172805

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative3LO() (gas: 11604239)
Logs:
  MARKET BUY 1PP 3LO :
  ITERATION  1  GAS USED:  210508
  ITERATION  2  GAS USED:  210509
  ITERATION  3  GAS USED:  210508
  ITERATION  4  GAS USED:  210508
  ITERATION  5  GAS USED:  210508
  ITERATION  6  GAS USED:  210509
  ITERATION  7  GAS USED:  210509
  ITERATION  8  GAS USED:  210509
  ITERATION  9  GAS USED:  210509
  ITERATION  10  GAS USED:  206217

[32m[PASS][0m testBenchmarkMarketSellDifferentPriceIterative() (gas: 12031901)
Logs:
  MARKET BUY 3PP 1LO : 
  ITERATION  1  GAS USED :  226833
  ITERATION  2  GAS USED :  226836
  ITERATION  3  GAS USED :  226946
  ITERATION  4  GAS USED :  228712
  ITERATION  5  GAS USED :  224579
  ITERATION  6  GAS USED :  226878
  ITERATION  7  GAS USED :  226737
  ITERATION  8  GAS USED :  226775
  ITERATION  9  GAS USED :  227096
  ITERATION  10  GAS USED :  228748

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative1LO() (gas: 10929689)
Logs:
  MARKET SELL 1PP 1LO: 
  ITERATION  1  GAS USED:  136576
  ITERATION  2  GAS USED:  135210
  ITERATION  3  GAS USED:  135210
  ITERATION  4  GAS USED:  135210
  ITERATION  5  GAS USED:  135210
  ITERATION  6  GAS USED:  135210
  ITERATION  7  GAS USED:  135210
  ITERATION  8  GAS USED:  135210
  ITERATION  9  GAS USED:  135211
  ITERATION  10  GAS USED:  135210

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative2LO() (gas: 11288199)
Logs:
  MARKET SELL 1PP 2LO: 
  ITERATION  1  GAS USED:  172091
  ITERATION  2  GAS USED:  172091
  ITERATION  3  GAS USED:  172091
  ITERATION  4  GAS USED:  172091
  ITERATION  5  GAS USED:  172091
  ITERATION  6  GAS USED:  172091
  ITERATION  7  GAS USED:  172091
  ITERATION  8  GAS USED:  172091
  ITERATION  9  GAS USED:  172092
  ITERATION  10  GAS USED:  172091

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative3LO() (gas: 11641741)
Logs:
  MARKET SELL 1PP 3LO: 
  ITERATION  1  GAS USED:  209605
  ITERATION  2  GAS USED:  209605
  ITERATION  3  GAS USED:  209605
  ITERATION  4  GAS USED:  209605
  ITERATION  5  GAS USED:  209605
  ITERATION  6  GAS USED:  209605
  ITERATION  7  GAS USED:  209605
  ITERATION  8  GAS USED:  209605
  ITERATION  9  GAS USED:  209606
  ITERATION  10  GAS USED:  205718

[32m[PASS][0m testBenchmarkSellSingleDifferentPriceSameUser() (gas: 1913306)
Logs:
  LIMIT SELL ORDER DIFFERENT PP: 
  ITERATION  1 GAS USED:  254896
  ITERATION  2 GAS USED:  154729
  ITERATION  3 GAS USED:  176972
  ITERATION  4 GAS USED:  154729
  ITERATION  5 GAS USED:  154730
  ITERATION  6 GAS USED:  176973
  ITERATION  7 GAS USED:  154730
  ITERATION  8 GAS USED:  154730
  ITERATION  9 GAS USED:  176973
  ITERATION  10 GAS USED:  149931

[32m[PASS][0m testBenchmarkSellSingleSamePriceSameUser() (gas: 1797469)
Logs:
  LIMIT SELL ORDER 1PP: 
  ITERATION  1 GAS USED:  254742
  ITERATION  2 GAS USED:  149470
  ITERATION  3 GAS USED:  149470
  ITERATION  4 GAS USED:  149470
  ITERATION  5 GAS USED:  149469
  ITERATION  6 GAS USED:  149470
  ITERATION  7 GAS USED:  149470
  ITERATION  8 GAS USED:  149470
  ITERATION  9 GAS USED:  149470
  ITERATION  10 GAS USED:  144671

Suite result: [32mok[0m. [32m14[0m passed; [31m0[0m failed; [33m0[0m skipped; finished in 30.29ms (112.53ms CPU time)

Ran 1 test suite in 233.20ms (30.29ms CPU time): [32m14[0m tests passed, [31m0[0m failed, [33m0[0m skipped (14 total tests)
