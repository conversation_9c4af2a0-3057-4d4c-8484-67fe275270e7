No files changed, compilation skipped

Ran 14 tests for test/test_benchmarks/BenchMarkingStorage.sol:BenchMarkTest
[32m[PASS][0m testBenchmarkBuySingleMultiplePriceDifferentUser() (gas: 3346686)
Logs:
  LIMIT BUY ORDER DIFFERENT PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  250698
  ITERATION  2  GAS USED:  172766
  ITERATION  3  GAS USED:  172766
  ITERATION  4  GAS USED:  172766
  ITERATION  5  GAS USED:  194898
  ITERATION  6  GAS USED:  172766
  ITERATION  7  GAS USED:  172766
  ITERATION  8  GAS USED:  172766
  ITERATION  9  GAS USED:  172766
  ITERATION  10  GAS USED:  172766

[32m[PASS][0m testBenchmarkBuySingleSamePriceDifferentUser() (gas: 3074252)
Logs:
  LIMIT BUY ORDERS SAME PRICE DIFFERENT USER : 
  ITERATION  1  GAS USED:  250617
  ITERATION  2  GAS USED:  145335
  ITERATION  3  GAS USED:  145335
  ITERATION  4  GAS USED:  145335
  ITERATION  5  GAS USED:  145335
  ITERATION  6  GAS USED:  145335
  ITERATION  7  GAS USED:  145335
  ITERATION  8  GAS USED:  145335
  ITERATION  9  GAS USED:  145335
  ITERATION  10  GAS USED:  145335

[32m[PASS][0m testBenchmarkCancelBuyOrders() (gas: 2290823)
Logs:
  CANCEL BUY ORDERS 1LO : 
  ITERATION  1  GAS USED:  67972
  ITERATION  2  GAS USED:  50872
  ITERATION  3  GAS USED:  50872
  ITERATION  4  GAS USED:  50872
  ITERATION  5  GAS USED:  50873
  ITERATION  6  GAS USED:  50873
  ITERATION  7  GAS USED:  50873
  ITERATION  8  GAS USED:  50873
  ITERATION  9  GAS USED:  50873
  ITERATION  10  GAS USED:  56947

[32m[PASS][0m testBenchmarkCancelSellOrders() (gas: 2284991)
Logs:
  CANCEL SELL ORDERS 1LO :
  ITERATION  1  GAS USED:  67782
  ITERATION  2  GAS USED:  50682
  ITERATION  3  GAS USED:  50682
  ITERATION  4  GAS USED:  50682
  ITERATION  5  GAS USED:  50682
  ITERATION  6  GAS USED:  50682
  ITERATION  7  GAS USED:  50682
  ITERATION  8  GAS USED:  50682
  ITERATION  9  GAS USED:  50683
  ITERATION  10  GAS USED:  56795

[32m[PASS][0m testBenchmarkMarketBuyDifferentPriceIterative() (gas: 12052042)
Logs:
  MARKET BUY 3PP 1LO:
  ITERATION  1  GAS USED:  239084
  ITERATION  2  GAS USED:  222182
  ITERATION  3  GAS USED:  222473
  ITERATION  4  GAS USED:  222460
  ITERATION  5  GAS USED:  219894
  ITERATION  6  GAS USED:  219675
  ITERATION  7  GAS USED:  222372
  ITERATION  8  GAS USED:  222221
  ITERATION  9  GAS USED:  222456
  ITERATION  10  GAS USED:  213983

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative1LO() (gas: 11164029)
Logs:
  MARKET BUY 1PP 1LO :
  ITERATION  1  GAS USED:  154637
  ITERATION  2  GAS USED:  137538
  ITERATION  3  GAS USED:  137537
  ITERATION  4  GAS USED:  137537
  ITERATION  5  GAS USED:  137537
  ITERATION  6  GAS USED:  137538
  ITERATION  7  GAS USED:  137538
  ITERATION  8  GAS USED:  137538
  ITERATION  9  GAS USED:  137538
  ITERATION  10  GAS USED:  137538

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative2LO() (gas: 11532287)
Logs:
  MARKET BUY 1PP 2LO :
  ITERATION  1  GAS USED:  191454
  ITERATION  2  GAS USED:  174355
  ITERATION  3  GAS USED:  174354
  ITERATION  4  GAS USED:  174354
  ITERATION  5  GAS USED:  174354
  ITERATION  6  GAS USED:  174355
  ITERATION  7  GAS USED:  174355
  ITERATION  8  GAS USED:  174355
  ITERATION  9  GAS USED:  174355
  ITERATION  10  GAS USED:  174355

[32m[PASS][0m testBenchmarkMarketBuySamePriceIterative3LO() (gas: 11858153)
Logs:
  MARKET BUY 1PP 3LO :
  ITERATION  1  GAS USED:  228547
  ITERATION  2  GAS USED:  211448
  ITERATION  3  GAS USED:  211447
  ITERATION  4  GAS USED:  211447
  ITERATION  5  GAS USED:  211447
  ITERATION  6  GAS USED:  211448
  ITERATION  7  GAS USED:  211448
  ITERATION  8  GAS USED:  211448
  ITERATION  9  GAS USED:  211448
  ITERATION  10  GAS USED:  195448

[32m[PASS][0m testBenchmarkMarketSellDifferentPriceIterative() (gas: 12091617)
Logs:
  MARKET BUY 3PP 1LO : 
  ITERATION  1  GAS USED :  237199
  ITERATION  2  GAS USED :  220103
  ITERATION  3  GAS USED :  220240
  ITERATION  4  GAS USED :  217648
  ITERATION  5  GAS USED :  217282
  ITERATION  6  GAS USED :  220155
  ITERATION  7  GAS USED :  219979
  ITERATION  8  GAS USED :  220026
  ITERATION  9  GAS USED :  220428
  ITERATION  10  GAS USED :  212663

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative1LO() (gas: 11205027)
Logs:
  MARKET SELL 1PP 1LO: 
  ITERATION  1  GAS USED:  154162
  ITERATION  2  GAS USED:  137062
  ITERATION  3  GAS USED:  137062
  ITERATION  4  GAS USED:  137062
  ITERATION  5  GAS USED:  137062
  ITERATION  6  GAS USED:  137062
  ITERATION  7  GAS USED:  137062
  ITERATION  8  GAS USED:  137062
  ITERATION  9  GAS USED:  137063
  ITERATION  10  GAS USED:  137062

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative2LO() (gas: 11572789)
Logs:
  MARKET SELL 1PP 2LO: 
  ITERATION  1  GAS USED:  190781
  ITERATION  2  GAS USED:  173681
  ITERATION  3  GAS USED:  173681
  ITERATION  4  GAS USED:  173681
  ITERATION  5  GAS USED:  173681
  ITERATION  6  GAS USED:  173681
  ITERATION  7  GAS USED:  173681
  ITERATION  8  GAS USED:  173681
  ITERATION  9  GAS USED:  173682
  ITERATION  10  GAS USED:  173681

[32m[PASS][0m testBenchmarkMarketSellSamePriceIterative3LO() (gas: 11896527)
Logs:
  MARKET SELL 1PP 3LO: 
  ITERATION  1  GAS USED:  227676
  ITERATION  2  GAS USED:  210576
  ITERATION  3  GAS USED:  210576
  ITERATION  4  GAS USED:  210576
  ITERATION  5  GAS USED:  210576
  ITERATION  6  GAS USED:  210576
  ITERATION  7  GAS USED:  210576
  ITERATION  8  GAS USED:  210576
  ITERATION  9  GAS USED:  210577
  ITERATION  10  GAS USED:  194882

[32m[PASS][0m testBenchmarkSellSingleDifferentPriceSameUser() (gas: 1914388)
Logs:
  LIMIT SELL ORDER DIFFERENT PP: 
  ITERATION  1 GAS USED:  255002
  ITERATION  2 GAS USED:  154835
  ITERATION  3 GAS USED:  177078
  ITERATION  4 GAS USED:  154835
  ITERATION  5 GAS USED:  154836
  ITERATION  6 GAS USED:  177079
  ITERATION  7 GAS USED:  154836
  ITERATION  8 GAS USED:  154836
  ITERATION  9 GAS USED:  177079
  ITERATION  10 GAS USED:  150037

[32m[PASS][0m testBenchmarkSellSingleSamePriceSameUser() (gas: 1798551)
Logs:
  LIMIT SELL ORDER 1PP: 
  ITERATION  1 GAS USED:  254848
  ITERATION  2 GAS USED:  149576
  ITERATION  3 GAS USED:  149576
  ITERATION  4 GAS USED:  149576
  ITERATION  5 GAS USED:  149575
  ITERATION  6 GAS USED:  149576
  ITERATION  7 GAS USED:  149576
  ITERATION  8 GAS USED:  149576
  ITERATION  9 GAS USED:  149576
  ITERATION  10 GAS USED:  144777

Suite result: [32mok[0m. [32m14[0m passed; [31m0[0m failed; [33m0[0m skipped; finished in 25.50ms (136.70ms CPU time)

Ran 1 test suite in 220.19ms (25.50ms CPU time): [32m14[0m tests passed, [31m0[0m failed, [33m0[0m skipped (14 total tests)
